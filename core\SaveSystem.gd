class_name SaveSystem
extends Node

## Persistent data management system with cloud sync capabilities
## Handles player progress, settings, and game state serialization

const SAVE_FILE_PATH = "user://save_game.dat"
const SETTINGS_FILE_PATH = "user://settings.cfg"
const BACKUP_FILE_PATH = "user://save_game_backup.dat"

var save_data: Dictionary = {}
var settings_data: Dictionary = {}
var is_loading: bool = false
var auto_backup_enabled: bool = true

# Default save structure
var default_save_data: Dictionary = {
	"version": "1.0.0",
	"player": {
		"level": 1,
		"experience": 0,
		"gold": 100,
		"gems": 0,
		"rune_powder": 0,
		"stats": {
			"attack": 10,
			"defense": 5,
			"health": 100,
			"crit_chance": 0.05,
			"crit_damage": 1.5,
			"attack_speed": 1.0,
			"move_speed": 5.0
		}
	},
	"inventory": {
		"items": [],
		"equipped_gear": {
			"weapon": null,
			"armor": null,
			"wings": null,
			"accessory": null
		}
	},
	"skills": {
		"unlocked_passives": [],
		"skill_points": 0,
		"active_skills": ["dash", "auto_attack"]
	},
	"progression": {
		"runs_completed": 0,
		"highest_wave": 0,
		"unlocked_biomes": ["forest"],
		"achievements": []
	},
	"settings": {
		"master_volume": 1.0,
		"sfx_volume": 1.0,
		"music_volume": 1.0,
		"graphics_quality": "medium",
		"auto_attack": true,
		"damage_numbers": true
	}
}

func _ready() -> void:
	_ensure_save_directory()
	print("SaveSystem initialized")

func _ensure_save_directory() -> void:
	var dir = DirAccess.open("user://")
	if not dir.dir_exists("user://"):
		dir.make_dir("user://")

## Core Save/Load Functions
func save_game_data() -> bool:
	if is_loading:
		print("SaveSystem: Cannot save while loading")
		return false
	
	# Create backup before saving
	if auto_backup_enabled and FileAccess.file_exists(SAVE_FILE_PATH):
		_create_backup()
	
	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.WRITE)
	if file == null:
		print("SaveSystem: Failed to open save file for writing")
		return false
	
	# Update save data with current game state
	_update_save_data_from_game_state()
	
	# Add timestamp and version info
	save_data["last_saved"] = Time.get_datetime_string_from_system()
	save_data["version"] = default_save_data.version
	
	var json_string = JSON.stringify(save_data)
	file.store_string(json_string)
	file.close()
	
	print("SaveSystem: Game data saved successfully")
	EventBus.notification_shown.emit("Game Saved", "success")
	return true

func load_game_data() -> bool:
	is_loading = true
	
	if not FileAccess.file_exists(SAVE_FILE_PATH):
		print("SaveSystem: No save file found, creating new save")
		save_data = default_save_data.duplicate(true)
		_apply_save_data_to_game_state()
		is_loading = false
		return true
	
	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
	if file == null:
		print("SaveSystem: Failed to open save file for reading")
		is_loading = false
		return false
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		print("SaveSystem: Failed to parse save file, using backup")
		is_loading = false
		return _load_backup()
	
	save_data = json.data
	
	# Validate and migrate save data if needed
	if not _validate_save_data():
		print("SaveSystem: Save data validation failed, using backup")
		is_loading = false
		return _load_backup()
	
	_apply_save_data_to_game_state()
	is_loading = false
	print("SaveSystem: Game data loaded successfully")
	return true

func _validate_save_data() -> bool:
	# Check for required fields
	var required_fields = ["version", "player", "inventory", "skills", "progression"]
	for field in required_fields:
		if not save_data.has(field):
			print("SaveSystem: Missing required field: %s" % field)
			return false
	
	# Version compatibility check
	if save_data.get("version", "") != default_save_data.version:
		print("SaveSystem: Version mismatch, attempting migration")
		return _migrate_save_data()
	
	return true

func _migrate_save_data() -> bool:
	# Handle save data migration between versions
	var old_version = save_data.get("version", "0.0.0")
	print("SaveSystem: Migrating from version %s to %s" % [old_version, default_save_data.version])
	
	# Add any missing fields from default save data
	for key in default_save_data:
		if not save_data.has(key):
			save_data[key] = default_save_data[key].duplicate(true)
	
	save_data["version"] = default_save_data.version
	return true

## Backup System
func _create_backup() -> void:
	var source = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
	var backup = FileAccess.open(BACKUP_FILE_PATH, FileAccess.WRITE)
	
	if source and backup:
		backup.store_string(source.get_as_text())
		source.close()
		backup.close()
		print("SaveSystem: Backup created")

func _load_backup() -> bool:
	if not FileAccess.file_exists(BACKUP_FILE_PATH):
		print("SaveSystem: No backup file found, using defaults")
		save_data = default_save_data.duplicate(true)
		_apply_save_data_to_game_state()
		return true
	
	var file = FileAccess.open(BACKUP_FILE_PATH, FileAccess.READ)
	if file == null:
		print("SaveSystem: Failed to open backup file")
		save_data = default_save_data.duplicate(true)
		_apply_save_data_to_game_state()
		return false
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		print("SaveSystem: Backup file corrupted, using defaults")
		save_data = default_save_data.duplicate(true)
		_apply_save_data_to_game_state()
		return false
	
	save_data = json.data
	_apply_save_data_to_game_state()
	print("SaveSystem: Loaded from backup")
	return true

## Game State Integration
func _update_save_data_from_game_state() -> void:
	# This will be called by other systems to update save data
	# For now, we'll emit a signal for systems to update their data
	EventBus.emit_signal("save_data_update_requested")

func _apply_save_data_to_game_state() -> void:
	# Apply loaded data to game systems
	EventBus.emit_signal("save_data_loaded", save_data)

## Data Access Functions
func get_player_data() -> Dictionary:
	return save_data.get("player", {})

func get_inventory_data() -> Dictionary:
	return save_data.get("inventory", {})

func get_skills_data() -> Dictionary:
	return save_data.get("skills", {})

func get_progression_data() -> Dictionary:
	return save_data.get("progression", {})

func update_player_data(data: Dictionary) -> void:
	save_data["player"] = data

func update_inventory_data(data: Dictionary) -> void:
	save_data["inventory"] = data

func update_skills_data(data: Dictionary) -> void:
	save_data["skills"] = data

func update_progression_data(data: Dictionary) -> void:
	save_data["progression"] = data

## Settings Management
func save_settings() -> bool:
	var config = ConfigFile.new()
	
	for section in settings_data:
		for key in settings_data[section]:
			config.set_value(section, key, settings_data[section][key])
	
	var error = config.save(SETTINGS_FILE_PATH)
	if error == OK:
		print("SaveSystem: Settings saved")
		return true
	else:
		print("SaveSystem: Failed to save settings")
		return false

func load_settings() -> bool:
	var config = ConfigFile.new()
	var error = config.load(SETTINGS_FILE_PATH)
	
	if error != OK:
		print("SaveSystem: No settings file found, using defaults")
		settings_data = default_save_data.settings.duplicate(true)
		return false
	
	settings_data = {}
	for section in config.get_sections():
		settings_data[section] = {}
		for key in config.get_section_keys(section):
			settings_data[section][key] = config.get_value(section, key)
	
	print("SaveSystem: Settings loaded")
	return true

func get_setting(category: String, key: String, default_value = null):
	return settings_data.get(category, {}).get(key, default_value)

func set_setting(category: String, key: String, value) -> void:
	if not settings_data.has(category):
		settings_data[category] = {}
	settings_data[category][key] = value

## Utility Functions
func delete_save_data() -> bool:
	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.WRITE)
	if file:
		file.close()
		DirAccess.remove_absolute(SAVE_FILE_PATH)
		print("SaveSystem: Save data deleted")
		return true
	return false

func export_save_data() -> String:
	return JSON.stringify(save_data)

func import_save_data(json_string: String) -> bool:
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		return false
	
	save_data = json.data
	return _validate_save_data()
