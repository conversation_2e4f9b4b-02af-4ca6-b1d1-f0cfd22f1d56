class_name Loot<PERSON>ana<PERSON>
extends Node

## Loot system managing item drops, rewards, and pickup mechanics
## Implements the Observer pattern for loot events

signal loot_spawned(loot_pickup: LootPickup)
signal loot_collected(item: Item, player: Node)
signal rare_loot_dropped(item: Item, position: Vector3)

@export var loot_despawn_time: float = 30.0
@export var auto_pickup_range: float = 2.0
@export var magnetic_pickup_range: float = 5.0
@export var loot_bounce_height: float = 1.0

# Loot tracking
var active_loot_pickups: Array[LootPickup] = []
var loot_drop_rates: Dictionary = {}

# References
var main_scene: Node3D
var player: Node3D

func _ready() -> void:
	_setup_loot_drop_rates()
	_connect_events()

func initialize(scene: Node3D) -> void:
	main_scene = scene
	player = scene.get_player() if scene.has_method("get_player") else null

func _setup_loot_drop_rates() -> void:
	# Configure drop rates for different sources
	loot_drop_rates = {
		"enemy_grunt": {
			"gold_min": 3,
			"gold_max": 8,
			"item_chance": 0.1,
			"rare_item_chance": 0.02
		},
		"enemy_elite": {
			"gold_min": 15,
			"gold_max": 25,
			"item_chance": 0.3,
			"rare_item_chance": 0.1
		},
		"enemy_boss": {
			"gold_min": 50,
			"gold_max": 100,
			"item_chance": 0.8,
			"rare_item_chance": 0.3
		},
		"destructible": {
			"gold_min": 1,
			"gold_max": 5,
			"item_chance": 0.05,
			"rare_item_chance": 0.01
		},
		"treasure_chest": {
			"gold_min": 20,
			"gold_max": 50,
			"item_chance": 0.6,
			"rare_item_chance": 0.2
		}
	}

func _connect_events() -> void:
	EventBus.enemy_killed.connect(_on_enemy_killed)
	EventBus.loot_dropped.connect(_on_loot_dropped)

func _process(delta: float) -> void:
	_update_loot_pickups(delta)
	_check_auto_pickup()

## Loot Generation
func generate_enemy_loot(enemy: Enemy) -> void:
	var loot_source = _get_enemy_loot_source(enemy.enemy_type)
	var drop_config = loot_drop_rates[loot_source]
	
	# Generate gold drop
	var gold_amount = randi_range(drop_config.gold_min, drop_config.gold_max)
	if gold_amount > 0:
		spawn_gold_pickup(enemy.global_position, gold_amount)
	
	# Check for item drops
	if randf() < drop_config.item_chance:
		var item_rarity = _determine_item_rarity(drop_config)
		var item = ItemFactory.create_random_item(enemy.level)
		if item:
			item.rarity = item_rarity
			spawn_item_pickup(enemy.global_position, item)
	
	# Check for rare item drops
	if randf() < drop_config.rare_item_chance:
		var rare_item = _generate_rare_item(enemy.level)
		if rare_item:
			spawn_item_pickup(enemy.global_position, rare_item)
			rare_loot_dropped.emit(rare_item, enemy.global_position)

func _get_enemy_loot_source(enemy_type: GameEnums.EnemyType) -> String:
	match enemy_type:
		GameEnums.EnemyType.GRUNT:
			return "enemy_grunt"
		GameEnums.EnemyType.ELITE:
			return "enemy_elite"
		GameEnums.EnemyType.BOSS, GameEnums.EnemyType.WORLD_BOSS:
			return "enemy_boss"
		_:
			return "enemy_grunt"

func _determine_item_rarity(drop_config: Dictionary) -> GameEnums.ItemRarity:
	var roll = randf()
	
	# Higher chance for better rarity from better sources
	var rare_threshold = drop_config.get("rare_item_chance", 0.1)
	
	if roll < rare_threshold * 0.1:  # 1% of rare chance for legendary+
		return GameEnums.ItemRarity.LEGENDARY
	elif roll < rare_threshold * 0.3:  # 3% of rare chance for epic
		return GameEnums.ItemRarity.EPIC
	elif roll < rare_threshold:  # Full rare chance for rare
		return GameEnums.ItemRarity.RARE
	elif roll < 0.3:  # 30% for uncommon
		return GameEnums.ItemRarity.UNCOMMON
	else:
		return GameEnums.ItemRarity.COMMON

func _generate_rare_item(level: int) -> Item:
	# Generate a guaranteed rare+ item
	var rarity = GameEnums.ItemRarity.RARE
	
	# Small chance for even better rarity
	var roll = randf()
	if roll < 0.05:  # 5% for legendary
		rarity = GameEnums.ItemRarity.LEGENDARY
	elif roll < 0.15:  # 10% for epic
		rarity = GameEnums.ItemRarity.EPIC
	
	return ItemFactory.create_random_item(level, GameEnums.ItemType.WEAPON)

## Loot Pickup Creation
func spawn_gold_pickup(position: Vector3, amount: int) -> LootPickup:
	var pickup = _create_loot_pickup("gold", position)
	pickup.setup_gold(amount)
	return pickup

func spawn_item_pickup(position: Vector3, item: Item) -> LootPickup:
	var pickup = _create_loot_pickup("item", position)
	pickup.setup_item(item)
	return pickup

func spawn_currency_pickup(position: Vector3, currency_type: String, amount: int) -> LootPickup:
	var pickup = _create_loot_pickup("currency", position)
	pickup.setup_currency(currency_type, amount)
	return pickup

func spawn_reward_item(rarity: GameEnums.ItemRarity, level: int) -> void:
	if not player:
		return
	
	var item = ItemFactory.create_random_item(level)
	item.rarity = rarity
	
	var spawn_position = player.global_position + Vector3(
		randf_range(-2, 2),
		2,
		randf_range(-2, 2)
	)
	
	spawn_item_pickup(spawn_position, item)

func _create_loot_pickup(pickup_type: String, position: Vector3) -> LootPickup:
	# Get pickup from object pool
	var pickup = ResourceManager.get_pooled_object("loot_pickup")
	if not pickup:
		pickup = _create_new_loot_pickup()
	
	# Set position with bounce effect
	pickup.global_position = position + Vector3(0, loot_bounce_height, 0)
	
	# Add to scene
	if main_scene:
		main_scene.add_child(pickup)
	else:
		get_tree().current_scene.add_child(pickup)
	
	# Track pickup
	active_loot_pickups.append(pickup)
	
	# Connect signals
	pickup.pickup_collected.connect(_on_pickup_collected)
	pickup.pickup_expired.connect(_on_pickup_expired)
	
	# Emit spawn signal
	loot_spawned.emit(pickup)
	
	return pickup

func _create_new_loot_pickup() -> LootPickup:
	# Create a basic loot pickup since we don't have the scene file yet
	var pickup = LootPickup.new()
	
	# Add visual representation
	var mesh_instance = MeshInstance3D.new()
	var mesh = SphereMesh.new()
	mesh.radius = 0.3
	mesh_instance.mesh = mesh
	pickup.add_child(mesh_instance)
	
	# Add collision for pickup detection
	var area = Area3D.new()
	var collision = CollisionShape3D.new()
	var shape = SphereShape3D.new()
	shape.radius = 0.5
	collision.shape = shape
	area.add_child(collision)
	pickup.add_child(area)
	
	# Add physics body for bouncing
	var rigid_body = RigidBody3D.new()
	var body_collision = CollisionShape3D.new()
	var body_shape = SphereShape3D.new()
	body_shape.radius = 0.3
	body_collision.shape = body_shape
	rigid_body.add_child(body_collision)
	pickup.add_child(rigid_body)
	
	return pickup

## Loot Pickup Management
func _update_loot_pickups(delta: float) -> void:
	# Update pickup timers and effects
	for i in range(active_loot_pickups.size() - 1, -1, -1):
		var pickup = active_loot_pickups[i]
		if not is_instance_valid(pickup):
			active_loot_pickups.remove_at(i)
			continue
		
		pickup.update_pickup(delta)
		
		# Check for magnetic attraction
		if player and pickup.can_be_attracted():
			var distance = pickup.global_position.distance_to(player.global_position)
			if distance <= magnetic_pickup_range:
				_attract_pickup_to_player(pickup, delta)

func _attract_pickup_to_player(pickup: LootPickup, delta: float) -> void:
	var direction = (player.global_position - pickup.global_position).normalized()
	var attraction_speed = 10.0
	pickup.global_position += direction * attraction_speed * delta

func _check_auto_pickup() -> void:
	if not player:
		return
	
	for pickup in active_loot_pickups:
		if not is_instance_valid(pickup):
			continue
		
		var distance = pickup.global_position.distance_to(player.global_position)
		if distance <= auto_pickup_range:
			pickup.collect_pickup(player)

## Event Handlers
func _on_enemy_killed(enemy: Node, experience: int) -> void:
	if enemy is Enemy:
		generate_enemy_loot(enemy)

func _on_loot_dropped(loot_data: Dictionary, position: Vector3) -> void:
	# Handle manual loot drops
	match loot_data.type:
		"gold":
			spawn_gold_pickup(position, loot_data.amount)
		"item":
			if loot_data.has("item"):
				spawn_item_pickup(position, loot_data.item)
		"currency":
			spawn_currency_pickup(position, loot_data.currency_type, loot_data.amount)

func _on_pickup_collected(pickup: LootPickup, collector: Node) -> void:
	# Remove from tracking
	var index = active_loot_pickups.find(pickup)
	if index != -1:
		active_loot_pickups.remove_at(index)
	
	# Process the pickup
	_process_pickup_collection(pickup, collector)
	
	# Return to pool
	ResourceManager.return_pooled_object("loot_pickup", pickup)
	
	loot_collected.emit(pickup.get_item(), collector)

func _on_pickup_expired(pickup: LootPickup) -> void:
	# Remove expired pickup
	var index = active_loot_pickups.find(pickup)
	if index != -1:
		active_loot_pickups.remove_at(index)
	
	ResourceManager.return_pooled_object("loot_pickup", pickup)

func _process_pickup_collection(pickup: LootPickup, collector: Node) -> void:
	match pickup.pickup_type:
		"gold":
			EventBus.currency_changed.emit("gold", pickup.get_amount())
			EventBus.notification_shown.emit("+%d Gold" % pickup.get_amount(), "loot")
		
		"item":
			var item = pickup.get_item()
			if collector.has_method("add_item_to_inventory"):
				collector.add_item_to_inventory(item)
			EventBus.item_picked_up.emit(item)
			EventBus.notification_shown.emit("Found: %s" % item.name, "loot")
		
		"currency":
			var currency_type = pickup.get_currency_type()
			var amount = pickup.get_amount()
			EventBus.currency_changed.emit(currency_type, amount)
			EventBus.notification_shown.emit("+%d %s" % [amount, currency_type.capitalize()], "loot")

## Special Loot Events
func spawn_treasure_chest(position: Vector3) -> void:
	var drop_config = loot_drop_rates["treasure_chest"]
	
	# Guaranteed gold
	var gold_amount = randi_range(drop_config.gold_min, drop_config.gold_max)
	spawn_gold_pickup(position, gold_amount)
	
	# High chance for items
	if randf() < drop_config.item_chance:
		var item = ItemFactory.create_random_item(GameManager.current_wave)
		item.rarity = _determine_item_rarity(drop_config)
		spawn_item_pickup(position + Vector3(1, 0, 0), item)
	
	# Chance for gems
	if randf() < 0.3:
		spawn_currency_pickup(position + Vector3(-1, 0, 0), "gems", randi_range(1, 3))

func spawn_boss_loot(position: Vector3, boss_level: int) -> void:
	# Guaranteed high-value loot for boss kills
	var gold_amount = randi_range(100, 200)
	spawn_gold_pickup(position, gold_amount)
	
	# Guaranteed rare+ item
	var item = ItemFactory.create_random_item(boss_level)
	item.rarity = GameEnums.ItemRarity.EPIC
	spawn_item_pickup(position + Vector3(1, 0, 1), item)
	
	# Guaranteed gems
	spawn_currency_pickup(position + Vector3(-1, 0, 1), "gems", randi_range(5, 10))
	
	# Chance for legendary item
	if randf() < 0.3:
		var legendary_item = ItemFactory.create_random_item(boss_level)
		legendary_item.rarity = GameEnums.ItemRarity.LEGENDARY
		spawn_item_pickup(position + Vector3(0, 0, 2), legendary_item)

func create_loot_explosion(position: Vector3, loot_count: int) -> void:
	# Create multiple loot pickups in a spread pattern
	for i in loot_count:
		var angle = (i * TAU) / loot_count
		var distance = randf_range(1.0, 3.0)
		var loot_position = position + Vector3(
			cos(angle) * distance,
			randf_range(0.5, 2.0),
			sin(angle) * distance
		)
		
		# Random loot type
		if randf() < 0.7:
			spawn_gold_pickup(loot_position, randi_range(5, 15))
		else:
			var item = ItemFactory.create_random_item(GameManager.current_wave)
			spawn_item_pickup(loot_position, item)

## Utility Functions
func get_active_loot_count() -> int:
	return active_loot_pickups.size()

func clear_all_loot() -> void:
	for pickup in active_loot_pickups:
		if is_instance_valid(pickup):
			ResourceManager.return_pooled_object("loot_pickup", pickup)
	active_loot_pickups.clear()

func get_loot_in_range(center: Vector3, range: float) -> Array[LootPickup]:
	var loot_in_range = []
	for pickup in active_loot_pickups:
		if is_instance_valid(pickup):
			var distance = pickup.global_position.distance_to(center)
			if distance <= range:
				loot_in_range.append(pickup)
	return loot_in_range

func set_auto_pickup_range(new_range: float) -> void:
	auto_pickup_range = new_range

func set_magnetic_pickup_range(new_range: float) -> void:
	magnetic_pickup_range = new_range

## Debug Functions
func debug_spawn_test_loot() -> void:
	if not player:
		return
	
	var pos = player.global_position
	spawn_gold_pickup(pos + Vector3(2, 0, 0), 50)
	
	var test_item = ItemFactory.create_item("sword_basic", GameEnums.ItemRarity.EPIC, 5)
	spawn_item_pickup(pos + Vector3(-2, 0, 0), test_item)
	
	spawn_currency_pickup(pos + Vector3(0, 0, 2), "gems", 5)

func debug_spawn_loot_explosion() -> void:
	if player:
		create_loot_explosion(player.global_position + Vector3(0, 0, 5), 10)

func debug_print_loot_state() -> void:
	print("=== Loot Manager Debug ===")
	print("Active Loot Pickups: %d" % active_loot_pickups.size())
	print("Auto Pickup Range: %.1f" % auto_pickup_range)
	print("Magnetic Range: %.1f" % magnetic_pickup_range)
	print("==========================")
