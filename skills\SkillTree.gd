class_name Skill<PERSON>ree
extends Node

## Skill tree system implementing passive ability progression
## Uses the Composite pattern for skill node relationships

signal skill_unlocked(skill_id: String, skill_data: Dictionary)
signal skill_point_spent(skill_id: String, points_remaining: int)
signal skill_tree_reset()

@export var available_skill_points: int = 0
@export var total_skill_points_earned: int = 0

# Skill tree data
var skill_nodes: Dictionary = {}
var unlocked_skills: Array[String] = []
var skill_levels: Dictionary = {}

# Skill categories
var skill_categories: Dictionary = {
	"combat": [],
	"defense": [],
	"utility": [],
	"mastery": []
}

func _ready() -> void:
	_initialize_skill_tree()
	_connect_events()

func _initialize_skill_tree() -> void:
	_create_skill_nodes()
	_setup_skill_dependencies()
	_setup_skill_categories()

func _connect_events() -> void:
	EventBus.player_level_up.connect(_on_player_level_up)
	EventBus.skill_point_spent.connect(_on_skill_point_spent)

## Skill Node Creation
func _create_skill_nodes() -> void:
	# Combat Skills
	skill_nodes["attack_power"] = _create_skill_node(
		"attack_power",
		"Attack Power",
		"Increases base attack damage",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.COMBAT,
		5,  # max_level
		{GameEnums.StatType.ATTACK: 2},  # bonus_per_level
		[]  # dependencies
	)
	
	skill_nodes["critical_strike"] = _create_skill_node(
		"critical_strike",
		"Critical Strike",
		"Increases critical hit chance and damage",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.COMBAT,
		3,
		{GameEnums.StatType.CRIT_CHANCE: 0.02, GameEnums.StatType.CRIT_DAMAGE: 0.1},
		["attack_power"]
	)
	
	skill_nodes["berserker_rage"] = _create_skill_node(
		"berserker_rage",
		"Berserker Rage",
		"Attack speed increases when health is low",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.COMBAT,
		1,
		{"berserker_threshold": 0.3, "attack_speed_bonus": 0.5},
		["critical_strike"]
	)
	
	# Defense Skills
	skill_nodes["health_boost"] = _create_skill_node(
		"health_boost",
		"Health Boost",
		"Increases maximum health",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.DEFENSIVE,
		5,
		{GameEnums.StatType.HEALTH: 10},
		[]
	)
	
	skill_nodes["armor_mastery"] = _create_skill_node(
		"armor_mastery",
		"Armor Mastery",
		"Increases defense and damage reduction",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.DEFENSIVE,
		3,
		{GameEnums.StatType.DEFENSE: 3, "damage_reduction": 0.02},
		["health_boost"]
	)
	
	skill_nodes["regeneration"] = _create_skill_node(
		"regeneration",
		"Regeneration",
		"Slowly regenerates health over time",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.DEFENSIVE,
		3,
		{"health_regen_rate": 1.0},
		["armor_mastery"]
	)
	
	# Utility Skills
	skill_nodes["movement_speed"] = _create_skill_node(
		"movement_speed",
		"Fleet Footed",
		"Increases movement speed",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.UTILITY,
		3,
		{GameEnums.StatType.MOVE_SPEED: 0.5},
		[]
	)
	
	skill_nodes["loot_finder"] = _create_skill_node(
		"loot_finder",
		"Treasure Hunter",
		"Increases gold and item find rates",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.UTILITY,
		5,
		{GameEnums.StatType.GOLD_FIND: 0.1, GameEnums.StatType.MAGIC_FIND: 0.05},
		["movement_speed"]
	)
	
	skill_nodes["experience_gain"] = _create_skill_node(
		"experience_gain",
		"Quick Learner",
		"Increases experience gain",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.UTILITY,
		3,
		{GameEnums.StatType.EXPERIENCE_GAIN: 0.15},
		["loot_finder"]
	)
	
	# Mastery Skills (High-tier)
	skill_nodes["weapon_mastery"] = _create_skill_node(
		"weapon_mastery",
		"Weapon Mastery",
		"Unlocks weapon-specific abilities",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.MASTERY,
		1,
		{"weapon_abilities": true},
		["critical_strike", "armor_mastery"]
	)
	
	skill_nodes["elemental_affinity"] = _create_skill_node(
		"elemental_affinity",
		"Elemental Affinity",
		"Adds elemental damage to attacks",
		GameEnums.SkillType.PASSIVE,
		GameEnums.SkillCategory.MASTERY,
		3,
		{"elemental_damage": 5, "elemental_chance": 0.2},
		["weapon_mastery"]
	)

func _create_skill_node(
	id: String,
	name: String,
	description: String,
	skill_type: GameEnums.SkillType,
	category: GameEnums.SkillCategory,
	max_level: int,
	bonus_per_level: Dictionary,
	dependencies: Array[String]
) -> Dictionary:
	return {
		"id": id,
		"name": name,
		"description": description,
		"skill_type": skill_type,
		"category": category,
		"max_level": max_level,
		"current_level": 0,
		"bonus_per_level": bonus_per_level,
		"dependencies": dependencies,
		"unlocked": false,
		"cost_per_level": 1
	}

func _setup_skill_dependencies() -> void:
	# Validate and setup dependency relationships
	for skill_id in skill_nodes:
		var skill = skill_nodes[skill_id]
		for dependency in skill.dependencies:
			if not skill_nodes.has(dependency):
				print("Warning: Skill %s has invalid dependency: %s" % [skill_id, dependency])

func _setup_skill_categories() -> void:
	# Organize skills by category
	for category in skill_categories:
		skill_categories[category].clear()
	
	for skill_id in skill_nodes:
		var skill = skill_nodes[skill_id]
		var category_name = GameEnums.SkillCategory.keys()[skill.category].to_lower()
		if skill_categories.has(category_name):
			skill_categories[category_name].append(skill_id)

## Skill Unlocking and Leveling
func can_unlock_skill(skill_id: String) -> bool:
	if not skill_nodes.has(skill_id):
		return false
	
	var skill = skill_nodes[skill_id]
	
	# Check if already at max level
	if skill.current_level >= skill.max_level:
		return false
	
	# Check skill points
	if available_skill_points < skill.cost_per_level:
		return false
	
	# Check dependencies
	for dependency in skill.dependencies:
		if not is_skill_unlocked(dependency):
			return false
	
	return true

func unlock_skill(skill_id: String) -> bool:
	if not can_unlock_skill(skill_id):
		return false
	
	var skill = skill_nodes[skill_id]
	
	# Spend skill point
	available_skill_points -= skill.cost_per_level
	skill.current_level += 1
	
	# Mark as unlocked if first level
	if skill.current_level == 1:
		skill.unlocked = true
		unlocked_skills.append(skill_id)
	
	# Apply skill effects
	_apply_skill_effects(skill_id, skill.current_level)
	
	# Emit signals
	skill_unlocked.emit(skill_id, skill)
	skill_point_spent.emit(skill_id, available_skill_points)
	EventBus.skill_unlocked.emit(skill_id)
	
	print("Unlocked skill: %s (Level %d)" % [skill.name, skill.current_level])
	return true

func _apply_skill_effects(skill_id: String, level: int) -> void:
	var skill = skill_nodes[skill_id]
	var total_bonuses = {}
	
	# Calculate total bonuses for current level
	for bonus_type in skill.bonus_per_level:
		var bonus_value = skill.bonus_per_level[bonus_type] * level
		total_bonuses[bonus_type] = bonus_value
	
	# Apply stat bonuses
	var stat_bonuses = {}
	for bonus_type in total_bonuses:
		if bonus_type in GameEnums.StatType.values():
			stat_bonuses[bonus_type] = total_bonuses[bonus_type]
	
	if not stat_bonuses.is_empty():
		EventBus.player_stats_changed.emit(stat_bonuses)
	
	# Apply special effects
	_apply_special_skill_effects(skill_id, total_bonuses)

func _apply_special_skill_effects(skill_id: String, bonuses: Dictionary) -> void:
	match skill_id:
		"berserker_rage":
			EventBus.emit_signal("berserker_skill_unlocked", bonuses)
		"regeneration":
			EventBus.emit_signal("regeneration_skill_unlocked", bonuses)
		"weapon_mastery":
			EventBus.emit_signal("weapon_mastery_unlocked")
		"elemental_affinity":
			EventBus.emit_signal("elemental_affinity_unlocked", bonuses)

## Skill Tree Management
func reset_skill_tree() -> bool:
	# Refund all skill points
	var refunded_points = 0
	
	for skill_id in skill_nodes:
		var skill = skill_nodes[skill_id]
		refunded_points += skill.current_level * skill.cost_per_level
		skill.current_level = 0
		skill.unlocked = false
	
	available_skill_points += refunded_points
	unlocked_skills.clear()
	
	# Remove all skill effects
	_remove_all_skill_effects()
	
	skill_tree_reset.emit()
	EventBus.skill_tree_updated.emit()
	
	print("Skill tree reset. Refunded %d skill points." % refunded_points)
	return true

func _remove_all_skill_effects() -> void:
	# Remove stat bonuses
	var negative_bonuses = {}
	for skill_id in skill_nodes:
		var skill = skill_nodes[skill_id]
		if skill.current_level > 0:
			for bonus_type in skill.bonus_per_level:
				if bonus_type in GameEnums.StatType.values():
					var total_bonus = skill.bonus_per_level[bonus_type] * skill.current_level
					negative_bonuses[bonus_type] = negative_bonuses.get(bonus_type, 0.0) - total_bonus
	
	if not negative_bonuses.is_empty():
		EventBus.player_stats_changed.emit(negative_bonuses)
	
	# Remove special effects
	EventBus.emit_signal("all_skill_effects_removed")

## Skill Information
func get_skill_info(skill_id: String) -> Dictionary:
	if not skill_nodes.has(skill_id):
		return {}
	
	var skill = skill_nodes[skill_id].duplicate()
	skill["can_unlock"] = can_unlock_skill(skill_id)
	skill["total_bonuses"] = _calculate_total_skill_bonuses(skill_id)
	skill["next_level_bonuses"] = _calculate_next_level_bonuses(skill_id)
	
	return skill

func _calculate_total_skill_bonuses(skill_id: String) -> Dictionary:
	var skill = skill_nodes[skill_id]
	var total_bonuses = {}
	
	for bonus_type in skill.bonus_per_level:
		total_bonuses[bonus_type] = skill.bonus_per_level[bonus_type] * skill.current_level
	
	return total_bonuses

func _calculate_next_level_bonuses(skill_id: String) -> Dictionary:
	var skill = skill_nodes[skill_id]
	var next_level_bonuses = {}
	
	if skill.current_level < skill.max_level:
		for bonus_type in skill.bonus_per_level:
			next_level_bonuses[bonus_type] = skill.bonus_per_level[bonus_type]
	
	return next_level_bonuses

func is_skill_unlocked(skill_id: String) -> bool:
	return skill_id in unlocked_skills

func get_skill_level(skill_id: String) -> int:
	if skill_nodes.has(skill_id):
		return skill_nodes[skill_id].current_level
	return 0

func get_skills_by_category(category: GameEnums.SkillCategory) -> Array[String]:
	var category_name = GameEnums.SkillCategory.keys()[category].to_lower()
	return skill_categories.get(category_name, [])

func get_unlocked_skills() -> Array[String]:
	return unlocked_skills.duplicate()

## Skill Points Management
func add_skill_points(points: int) -> void:
	available_skill_points += points
	total_skill_points_earned += points
	EventBus.skill_point_spent.emit("", available_skill_points)

func get_available_skill_points() -> int:
	return available_skill_points

func get_total_skill_points_spent() -> int:
	return total_skill_points_earned - available_skill_points

## Event Handlers
func _on_player_level_up(new_level: int) -> void:
	# Award skill points on level up
	add_skill_points(GameConstants.SKILL_POINTS_PER_LEVEL)
	print("Level up! Gained %d skill point(s). Total: %d" % [GameConstants.SKILL_POINTS_PER_LEVEL, available_skill_points])

func _on_skill_point_spent(skill_id: String, points_remaining: int) -> void:
	# Handle external skill point spending
	if skill_id.is_empty():  # Points added
		available_skill_points = points_remaining

## Utility Functions
func get_skill_tree_completion() -> float:
	var total_levels = 0
	var current_levels = 0
	
	for skill_id in skill_nodes:
		var skill = skill_nodes[skill_id]
		total_levels += skill.max_level
		current_levels += skill.current_level
	
	return float(current_levels) / float(total_levels) if total_levels > 0 else 0.0

func get_mastery_skills_unlocked() -> int:
	var mastery_skills = get_skills_by_category(GameEnums.SkillCategory.MASTERY)
	var unlocked_count = 0
	
	for skill_id in mastery_skills:
		if is_skill_unlocked(skill_id):
			unlocked_count += 1
	
	return unlocked_count

## Serialization
func to_dict() -> Dictionary:
	var skill_levels_data = {}
	for skill_id in skill_nodes:
		skill_levels_data[skill_id] = skill_nodes[skill_id].current_level
	
	return {
		"available_skill_points": available_skill_points,
		"total_skill_points_earned": total_skill_points_earned,
		"skill_levels": skill_levels_data,
		"unlocked_skills": unlocked_skills
	}

func from_dict(data: Dictionary) -> void:
	available_skill_points = data.get("available_skill_points", 0)
	total_skill_points_earned = data.get("total_skill_points_earned", 0)
	
	var skill_levels_data = data.get("skill_levels", {})
	for skill_id in skill_levels_data:
		if skill_nodes.has(skill_id):
			var level = skill_levels_data[skill_id]
			skill_nodes[skill_id].current_level = level
			if level > 0:
				skill_nodes[skill_id].unlocked = true
				_apply_skill_effects(skill_id, level)
	
	unlocked_skills = data.get("unlocked_skills", [])

## Debug Functions
func debug_unlock_all_skills() -> void:
	for skill_id in skill_nodes:
		var skill = skill_nodes[skill_id]
		available_skill_points += skill.max_level * skill.cost_per_level
		
		for i in skill.max_level:
			unlock_skill(skill_id)

func debug_print_skill_tree() -> void:
	print("=== Skill Tree Debug ===")
	print("Available Points: %d" % available_skill_points)
	print("Total Points Earned: %d" % total_skill_points_earned)
	print("Completion: %.1f%%" % (get_skill_tree_completion() * 100))
	
	for category in skill_categories:
		print("\n%s Skills:" % category.capitalize())
		for skill_id in skill_categories[category]:
			var skill = skill_nodes[skill_id]
			print("  %s: %d/%d" % [skill.name, skill.current_level, skill.max_level])
	print("========================")
