class_name GameManager
extends Node

## Main game state controller following the Singleton pattern
## Manages overall game flow, state transitions, and core systems coordination

enum GameState {
	MAIN_MENU,
	IN_GAME,
	PAUSED,
	INVENTORY,
	SKILL_TREE,
	SETTINGS,
	LOADING
}

enum RunState {
	NOT_STARTED,
	IN_PROGRESS,
	COMPLETED,
	FAILED
}

@export var debug_mode: bool = false
@export var auto_save_interval: float = 30.0

var current_game_state: GameState = GameState.MAIN_MENU
var current_run_state: RunState = RunState.NOT_STARTED
var current_biome: String = ""
var current_wave: int = 0
var run_start_time: float = 0.0

# Game configuration
var game_config: Dictionary = {
	"version": "0.1.0",
	"max_waves_per_run": 20,
	"base_enemy_count": 5,
	"wave_scaling_factor": 1.2,
	"experience_multiplier": 1.0,
	"loot_drop_rate": 0.15
}

# Runtime statistics
var session_stats: Dictionary = {
	"runs_completed": 0,
	"enemies_killed": 0,
	"damage_dealt": 0,
	"items_collected": 0,
	"playtime_seconds": 0
}

var auto_save_timer: Timer

func _ready() -> void:
	_setup_auto_save()
	_connect_events()
	_initialize_systems()
	print("GameManager initialized - Version %s" % game_config.version)

func _setup_auto_save() -> void:
	auto_save_timer = Timer.new()
	auto_save_timer.wait_time = auto_save_interval
	auto_save_timer.timeout.connect(_on_auto_save_timeout)
	auto_save_timer.autostart = true
	add_child(auto_save_timer)

func _connect_events() -> void:
	EventBus.game_started.connect(_on_game_started)
	EventBus.game_paused.connect(_on_game_paused)
	EventBus.game_resumed.connect(_on_game_resumed)
	EventBus.run_completed.connect(_on_run_completed)
	EventBus.enemy_killed.connect(_on_enemy_killed)
	EventBus.item_picked_up.connect(_on_item_picked_up)

func _initialize_systems() -> void:
	# Initialize core systems in proper order
	if SaveSystem:
		SaveSystem.load_game_data()
	
	if ResourceManager:
		ResourceManager.preload_essential_resources()

## State Management
func change_game_state(new_state: GameState) -> void:
	if current_game_state == new_state:
		return
		
	var previous_state = current_game_state
	current_game_state = new_state
	
	_handle_state_transition(previous_state, new_state)
	print("GameManager: State changed from %s to %s" % [
		GameState.keys()[previous_state], 
		GameState.keys()[new_state]
	])

func _handle_state_transition(from_state: GameState, to_state: GameState) -> void:
	# Handle state exit logic
	match from_state:
		GameState.IN_GAME:
			if to_state != GameState.PAUSED:
				_pause_game_systems()
		GameState.PAUSED:
			_resume_game_systems()
	
	# Handle state entry logic
	match to_state:
		GameState.MAIN_MENU:
			_reset_run_state()
		GameState.IN_GAME:
			_start_game_systems()
		GameState.PAUSED:
			get_tree().paused = true
		GameState.LOADING:
			_show_loading_screen()

## Run Management
func start_new_run(biome: String) -> void:
	current_biome = biome
	current_run_state = RunState.IN_PROGRESS
	current_wave = 1
	run_start_time = Time.get_time_dict_from_system()["unix"]
	
	change_game_state(GameState.IN_GAME)
	EventBus.run_started.emit(biome)
	EventBus.wave_started.emit(current_wave)
	
	print("GameManager: Started new run in %s biome" % biome)

func complete_current_wave() -> void:
	if current_run_state != RunState.IN_PROGRESS:
		return
		
	EventBus.wave_completed.emit(current_wave)
	current_wave += 1
	
	if current_wave > game_config.max_waves_per_run:
		_complete_run(true)
	else:
		EventBus.wave_started.emit(current_wave)

func _complete_run(success: bool) -> void:
	current_run_state = RunState.COMPLETED if success else RunState.FAILED
	var run_duration = Time.get_time_dict_from_system()["unix"] - run_start_time
	
	session_stats.runs_completed += 1
	session_stats.playtime_seconds += run_duration
	
	var rewards = _calculate_run_rewards(success, run_duration)
	EventBus.run_completed.emit(success, rewards)
	
	print("GameManager: Run %s after %d seconds" % [
		"completed" if success else "failed", 
		run_duration
	])

func _calculate_run_rewards(success: bool, duration: float) -> Array:
	var rewards = []
	
	if success:
		var base_gold = current_wave * 10
		var time_bonus = max(0, 300 - duration) * 2  # Bonus for fast completion
		rewards.append({"type": "gold", "amount": base_gold + time_bonus})
		
		# Add experience based on waves completed
		var experience = current_wave * 25
		rewards.append({"type": "experience", "amount": experience})
	
	return rewards

func _reset_run_state() -> void:
	current_run_state = RunState.NOT_STARTED
	current_biome = ""
	current_wave = 0
	run_start_time = 0.0

## System Control
func _start_game_systems() -> void:
	get_tree().paused = false
	# Additional system startup logic here

func _pause_game_systems() -> void:
	get_tree().paused = true

func _resume_game_systems() -> void:
	get_tree().paused = false

func _show_loading_screen() -> void:
	# Implement loading screen logic
	pass

## Event Handlers
func _on_game_started() -> void:
	change_game_state(GameState.IN_GAME)

func _on_game_paused() -> void:
	change_game_state(GameState.PAUSED)

func _on_game_resumed() -> void:
	change_game_state(GameState.IN_GAME)

func _on_run_completed(success: bool, rewards: Array) -> void:
	change_game_state(GameState.MAIN_MENU)

func _on_enemy_killed(enemy: Node, experience: int) -> void:
	session_stats.enemies_killed += 1

func _on_item_picked_up(item: Item) -> void:
	session_stats.items_collected += 1

func _on_auto_save_timeout() -> void:
	if SaveSystem and current_game_state != GameState.LOADING:
		SaveSystem.save_game_data()
		if debug_mode:
			print("GameManager: Auto-save completed")

## Utility Functions
func get_current_state_name() -> String:
	return GameState.keys()[current_game_state]

func get_run_progress() -> float:
	if current_run_state != RunState.IN_PROGRESS:
		return 0.0
	return float(current_wave) / float(game_config.max_waves_per_run)

func is_game_active() -> bool:
	return current_game_state == GameState.IN_GAME and current_run_state == RunState.IN_PROGRESS

## Debug Functions
func debug_print_state() -> void:
	if debug_mode:
		print("=== GameManager Debug Info ===")
		print("Game State: %s" % get_current_state_name())
		print("Run State: %s" % RunState.keys()[current_run_state])
		print("Current Wave: %d/%d" % [current_wave, game_config.max_waves_per_run])
		print("Session Stats: %s" % session_stats)
		print("==============================")
