class_name GameE<PERSON>s
extends RefCounted

## Central repository for all game enumerations
## Provides type safety and consistent naming across the codebase

# Item System Enums
enum ItemType {
	WEAPON,
	ARMOR,
	WINGS,
	ACCESSORY,
	CONSUMABLE,
	MATERIAL,
	CURRENCY
}

enum ItemRarity {
	COMM<PERSON>,
	UNCOMMON,
	RARE,
	EPIC,
	<PERSON><PERSON><PERSON>DARY,
	MYTH<PERSON>,
	DIVINE
}

enum WeaponType {
	SWORD,
	STAFF,
	BOW,
	DAGGER,
	HAMMER,
	SPEAR
}

enum ArmorType {
	LIGHT,
	MEDIUM,
	HEAVY,
	ROBE
}

enum SocketType {
	ATTACK,
	DEFENSE,
	UTILITY,
	SPECIAL
}

# Character System Enums
enum StatType {
	ATTACK,
	DEFENSE,
	HEALTH,
	MANA,
	CRIT_CHANCE,
	CRIT_DAMAGE,
	ATTACK_SPEED,
	MOVE_SPEED,
	EXPERIENCE_GAIN,
	GOLD_FIND,
	MAGIC_FIND
}

enum DamageType {
	PHYSICA<PERSON>,
	MAGI<PERSON><PERSON>,
	TRUE,
	CR<PERSON><PERSON><PERSON>,
	<PERSON>X<PERSON><PERSON>EN<PERSON>,
	<PERSON>OU<PERSON><PERSON>
}

enum StatusEffect {
	<PERSON><PERSON>IS<PERSON>,
	BUR<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	STUN,
	<PERSON>OW,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	BERSERK,
	INVISIBLE
}

# Skill System Enums
enum SkillType {
	ACTIVE,
	PASSIVE,
	ULTIMATE
}

enum SkillCategory {
	COMBAT,
	MOVEMENT,
	UTILITY,
	DEFENSIVE,
	OFFENSIVE,
	MASTERY
}

enum TargetType {
	SELF,
	ENEMY,
	ALLY,
	GROUND,
	AREA
}

# Combat System Enums
enum AttackResult {
	MISS,
	HIT,
	CRITICAL,
	EXCELLENT,
	DOUBLE,
	BLOCKED,
	DODGED
}

## Utility Functions
static func get_rarity_color(rarity: ItemRarity) -> Color:
	match rarity:
		ItemRarity.COMMON:
			return Color.WHITE
		ItemRarity.UNCOMMON:
			return Color.GREEN
		ItemRarity.RARE:
			return Color.BLUE
		ItemRarity.EPIC:
			return Color.PURPLE
		ItemRarity.LEGENDARY:
			return Color.ORANGE
		ItemRarity.MYTHIC:
			return Color.RED
		ItemRarity.DIVINE:
			return Color.GOLD
		_:
			return Color.WHITE

enum CombatState {
	IDLE,
	ATTACKING,
	CASTING,
	STUNNED,
	DEAD
}

# Enemy System Enums
enum EnemyType {
	GRUNT,
	ELITE,
	BOSS,
	WORLD_BOSS,
	MINION
}

enum EnemyBehavior {
	AGGRESSIVE,
	DEFENSIVE,
	RANGED,
	SUPPORT,
	BERSERKER,
	CASTER
}

enum AIState {
	IDLE,
	PATROL,
	CHASE,
	ATTACK,
	RETREAT,
	DEAD
}

# Level System Enums
enum BiomeType {
	FOREST,
	DESERT,
	ICE,
	VOLCANO,
	SHADOW,
	CELESTIAL
}

enum RoomType {
	COMBAT,
	TREASURE,
	BOSS,
	SHOP,
	SHRINE,
	TRAP
}

enum TrapType {
	SPIKE,
	FIRE,
	ICE,
	POISON,
	TELEPORT,
	SLOW
}

# UI System Enums
enum UIPanel {
	MAIN_MENU,
	HUD,
	INVENTORY,
	SKILLS,
	SETTINGS,
	SHOP,
	ACHIEVEMENTS,
	LEADERBOARD
}

enum NotificationType {
	INFO,
	SUCCESS,
	WARNING,
	ERROR,
	ACHIEVEMENT,
	LOOT
}

enum ButtonState {
	NORMAL,
	HOVER,
	PRESSED,
	DISABLED
}

# Audio System Enums
enum AudioType {
	SFX,
	MUSIC,
	VOICE,
	AMBIENT
}

enum MusicTrack {
	MAIN_MENU,
	FOREST_COMBAT,
	DESERT_COMBAT,
	ICE_COMBAT,
	BOSS_FIGHT,
	VICTORY,
	DEFEAT
}

# VFX System Enums
enum EffectType {
	HIT,
	SPELL,
	BUFF,
	DEBUFF,
	ENVIRONMENTAL,
	UI
}

enum ParticlePreset {
	FIRE,
	ICE,
	LIGHTNING,
	POISON,
	HEAL,
	EXPLOSION,
	SPARKLE
}

# Progression System Enums
enum AchievementType {
	COMBAT,
	EXPLORATION,
	COLLECTION,
	PROGRESSION,
	SOCIAL,
	SPECIAL
}

enum CurrencyType {
	GOLD,
	GEMS,
	RUNE_POWDER,
	EXPERIENCE,
	SKILL_POINTS
}

# Multiplayer System Enums
enum PlayerState {
	OFFLINE,
	ONLINE,
	IN_GAME,
	IN_HUB,
	AWAY
}

enum ChatChannel {
	GLOBAL,
	LOCAL,
	GUILD,
	WHISPER,
	SYSTEM
}

# Mobile System Enums
enum InputType {
	TOUCH,
	DRAG,
	PINCH,
	SWIPE,
	TAP,
	LONG_PRESS
}

enum PerformanceLevel {
	LOW,
	MEDIUM,
	HIGH,
	ULTRA
}

# Utility Functions for Enum Handling
static func get_rarity_color(rarity: ItemRarity) -> Color:
	match rarity:
		ItemRarity.COMMON:
			return Color.WHITE
		ItemRarity.UNCOMMON:
			return Color.GREEN
		ItemRarity.RARE:
			return Color.BLUE
		ItemRarity.EPIC:
			return Color.PURPLE
		ItemRarity.LEGENDARY:
			return Color.ORANGE
		ItemRarity.MYTHIC:
			return Color.RED
		ItemRarity.DIVINE:
			return Color.GOLD
		_:
			return Color.WHITE

static func get_rarity_name(rarity: ItemRarity) -> String:
	return ItemRarity.keys()[rarity]

static func get_damage_type_color(damage_type: DamageType) -> Color:
	match damage_type:
		DamageType.PHYSICAL:
			return Color.WHITE
		DamageType.MAGICAL:
			return Color.CYAN
		DamageType.TRUE:
			return Color.PURPLE
		DamageType.CRITICAL:
			return Color.YELLOW
		DamageType.EXCELLENT:
			return Color.ORANGE
		DamageType.DOUBLE:
			return Color.RED
		_:
			return Color.WHITE

static func get_biome_color(biome: BiomeType) -> Color:
	match biome:
		BiomeType.FOREST:
			return Color.GREEN
		BiomeType.DESERT:
			return Color.YELLOW
		BiomeType.ICE:
			return Color.CYAN
		BiomeType.VOLCANO:
			return Color.RED
		BiomeType.SHADOW:
			return Color.PURPLE
		BiomeType.CELESTIAL:
			return Color.GOLD
		_:
			return Color.WHITE

static func get_stat_icon(stat: StatType) -> String:
	match stat:
		StatType.ATTACK:
			return "⚔️"
		StatType.DEFENSE:
			return "🛡️"
		StatType.HEALTH:
			return "❤️"
		StatType.MANA:
			return "💙"
		StatType.CRIT_CHANCE:
			return "⚡"
		StatType.CRIT_DAMAGE:
			return "💥"
		StatType.ATTACK_SPEED:
			return "⏱️"
		StatType.MOVE_SPEED:
			return "👟"
		StatType.EXPERIENCE_GAIN:
			return "📈"
		StatType.GOLD_FIND:
			return "💰"
		StatType.MAGIC_FIND:
			return "🔮"
		_:
			return "❓"

static func is_positive_stat(stat: StatType) -> bool:
	# All stats are positive in this game design
	return true

static func get_notification_color(type: NotificationType) -> Color:
	match type:
		NotificationType.INFO:
			return Color.WHITE
		NotificationType.SUCCESS:
			return Color.GREEN
		NotificationType.WARNING:
			return Color.YELLOW
		NotificationType.ERROR:
			return Color.RED
		NotificationType.ACHIEVEMENT:
			return Color.GOLD
		NotificationType.LOOT:
			return Color.PURPLE
		_:
			return Color.WHITE
